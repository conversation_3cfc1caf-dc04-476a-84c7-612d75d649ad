<script setup lang="ts">
defineOptions({
  name: 'AddRules'
})

const visible = defineModel({ default: false })

// const {} = defineProps<{}>()

const rules = ref<any>([{ one: '', two: '', three: '', four: '' }])

const handleOpen = () => {
  visible.value = true
}
const handleClose = () => {
  visible.value = false
}
const handleCancel = () => {
  visible.value = false
}
const handleConfirn = () => {
  visible.value = false
}

const options0 = [
  {
    itemText: '且',
    itemValue: 1
  },
  {
    itemText: '或',
    itemValue: 2
  }
]

const options1 = [
  {
    itemText: '最长边',
    itemValue: 1
  },
  {
    itemText: '重量',
    itemValue: 2
  },
  {
    itemText: '物品属性',
    itemValue: 3
  }
]

const options2 = [
  {
    itemText: '大于',
    itemValue: 1
  },
  {
    itemText: '小于',
    itemValue: 2
  },
  {
    itemText: '等于',
    itemValue: 3
  }
]

const handleAddRule = () => {
  rules.value.push({ one: '', two: '', three: '', four: '' })
}
</script>

<template>
  <el-dialog
    v-model="visible"
    title="条件设置"
    width="700"
    @open="handleOpen"
    @close="handleClose"
  >
    <div>
      <div
        class="flex items-center"
        v-for="(item, index) in rules"
        :key="index"
        :class="{ 'mt-16': index !== 0 }"
      >
        <div>
          <div v-if="index === 0" class="!w-70 text-center">如果</div>
          <DSelect
            v-else
            v-model="item.one"
            :options="options0"
            placeholder="请选择"
            class="!w-70"
          ></DSelect>
        </div>
        <DSelect
          v-model="item.two"
          :options="options1"
          placeholder="请选择"
          class="!w-150 ml-8"
        ></DSelect>
        <DSelect
          v-model="item.three"
          :options="options2"
          placeholder="请选择"
          class="!w-150 ml-8"
        ></DSelect>

        <el-input
          v-model="item.four"
          clearable
          placeholder=""
          class="!w-240 ml-8"
        >
          <template #append>单位符号</template>
        </el-input>

        <el-button v-if="index !== 0" type="primary" link class="ml-8">
          <div class="i-ep:delete"></div>
        </el-button>
      </div>

      <el-button type="primary" link class="mt-16" @click="handleAddRule"
        >添加</el-button
      >
    </div>
    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirn"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
