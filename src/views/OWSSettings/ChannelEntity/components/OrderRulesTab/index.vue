<script setup lang="ts">
import { useTable } from '@/hooks/useTable'
import {
  deleteFirstVesselSetting,
  getFirstVesselSettingList
} from '@/api/initialServiceSettings'
import type { FirstVesselSettingEntity } from '@/api/initialServiceSettings/types'
import { useToggle } from '@/hooks/useToggle'
import AddRules from './AddRules.vue'

defineOptions({
  name: 'OrderRulesTab'
})

const {
  tableState: { pageSize, currentPage, total, loading, dataList },
  tableMethods
} = useTable<FirstVesselSettingEntity>({
  immediate: true,
  initialFormData: {
    transportServiceName: undefined,
    transportServiceCode: undefined,
    chargeMode: undefined,
    weightMode: undefined,
    arrivalCountry: undefined,
    destinationType: ''
  },
  fetchDataApi: async () => {
    const res = await getFirstVesselSettingList({
      pageSize: pageSize.value,
      pageNum: currentPage.value
    })
    return {
      list: res.result.records,
      total: res.result.total
    }
  },
  fetchDelApi: async (record: any) => {
    if (Array.isArray(record)) {
      const ids = record.map((item: any) => item.id?.toString())
      const res = await deleteFirstVesselSetting({ ids })
      return !!res
    } else {
      const res = await deleteFirstVesselSetting({ ids: [record.id] })
      return !!res
    }
  }
})

const tableRef = ref()
const [accVisible, handleAcc] = useToggle()
</script>

<template>
  <div>
    <div class="mb-18 flex justify-between w-full">
      <el-button @click="() => handleAcc()">添加条件</el-button>

      <div class="flex">
        <div class="flex items-center">
          <span class="mr-6">生效模式:</span>
          <DRadioGroup
            :options="[
              { itemText: '满足所有条件', itemValue: 1 },
              { itemText: '满足任一条件', itemValue: 2 }
            ]"
          ></DRadioGroup>
        </div>
        <div class="flex items-center ml-10">
          <span class="mr-6">单位:</span>
          <DRadioGroup
            :options="[
              { itemText: 'kg,cn,CMB', itemValue: 1 },
              { itemText: 'lb,in,CBF', itemValue: 2 }
            ]"
          ></DRadioGroup>
        </div>
      </div>
    </div>
    <el-table
      v-loading="loading"
      ref="tableRef"
      :data="dataList"
      :border="true"
      class="w-full"
      @selection-change="val => tableMethods.handleSelectionChange(val)"
    >
      <el-table-column label="序号" prop="transportServiceName" width="55" />
      <el-table-column label="条件详情" prop="transportServiceCode" />

      <el-table-column label="操作" fixed="right" width="260">
        <template #default="{}">
          <el-button link type="primary">编辑</el-button>
          <el-button link type="danger">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, ->, sizes, prev, pager, next, jumper"
      :total="total"
      class="mt-16"
    />

    <AddRules v-model="accVisible" />
  </div>
</template>

<style lang="scss" scoped></style>
