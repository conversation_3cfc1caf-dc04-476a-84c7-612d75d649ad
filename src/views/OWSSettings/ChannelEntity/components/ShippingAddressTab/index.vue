<script setup lang="ts">
import { useTable } from '@/hooks/useTable'
import {
  deleteFirstVesselSetting,
  getFirstVesselSettingList
} from '@/api/initialServiceSettings'
import type { FirstVesselSettingEntity } from '@/api/initialServiceSettings/types'
import { useToggle } from '@/hooks/useToggle'
import AddAddress from './AddAddress.vue'

defineOptions({
  name: 'ShippingAddressTab'
})

const {
  tableState: { pageSize, currentPage, total, loading, dataList },
  tableMethods
} = useTable<FirstVesselSettingEntity>({
  immediate: true,
  initialFormData: {
    transportServiceName: undefined,
    transportServiceCode: undefined,
    chargeMode: undefined,
    weightMode: undefined,
    arrivalCountry: undefined,
    destinationType: ''
  },
  fetchDataApi: async () => {
    const res = await getFirstVesselSettingList({
      pageSize: pageSize.value,
      pageNum: currentPage.value
    })
    return {
      list: res.result.records,
      total: res.result.total
    }
  },
  fetchDelApi: async (record: any) => {
    if (Array.isArray(record)) {
      const ids = record.map((item: any) => item.id?.toString())
      const res = await deleteFirstVesselSetting({ ids })
      return !!res
    } else {
      const res = await deleteFirstVesselSetting({ ids: [record.id] })
      return !!res
    }
  }
})

const tableRef = ref()
const [acVisible, handleAc] = useToggle()
</script>

<template>
  <div>
    <div class="mb-18">
      <el-button @click="() => handleAc()">添加地址</el-button>
    </div>
    <el-table
      v-loading="loading"
      ref="tableRef"
      :data="dataList"
      :border="true"
      class="w-full"
      @selection-change="val => tableMethods.handleSelectionChange(val)"
    >
      <el-table-column label="地址名称" prop="transportServiceCode" />
      <el-table-column label="仓库" prop="transportServiceName" />
      <el-table-column label="发件人" prop="chargeModeName" width="180" />
      <el-table-column label="国家/地区" prop="weightModeName" />
      <el-table-column label="省/州" prop="bubbleCoefficient" width="180" />
      <el-table-column label="城市" prop="arrivalCountry" width="180" />
      <el-table-column label="邮编" prop="arrivalCountry" width="180" />
      <el-table-column
        label="服务商地址代码"
        prop="arrivalCountry"
        width="180"
      />

      <el-table-column label="操作" fixed="right" width="260">
        <template #default="{}">
          <el-button link type="primary">编辑</el-button>
          <el-button link type="danger">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, ->, sizes, prev, pager, next, jumper"
      :total="total"
      class="mt-16"
    />

    <AddAddress v-model="acVisible" />
  </div>
</template>

<style lang="scss" scoped></style>
