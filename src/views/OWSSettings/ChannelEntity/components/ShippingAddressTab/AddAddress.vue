<script setup lang="ts">
defineOptions({
  name: 'AddChannel'
})

const visible = defineModel({ default: false })

// const {} = defineProps<{}>()

const formData = ref<any>({
  name: '',
  num: 0
})

const rules = ref<any>([])

const handleOpen = () => {
  visible.value = true
}
const handleClose = () => {
  visible.value = false
}
const handleCancel = () => {
  visible.value = false
}
const handleConfirn = () => {
  visible.value = false
}
</script>

<template>
  <el-dialog
    v-model="visible"
    title="设置发货地址"
    width="700"
    @open="handleOpen"
    @close="handleClose"
  >
    <el-form
      :model="formData"
      :rules="rules"
      ref="formDataRef"
      label-position="left"
      @submit.prevent
    >
      <el-row :gutter="16" class="w-full">
        <el-col :span="12">
          <el-form-item label="地址名称" prop="userName">
            <el-input
              v-model.trim="formData.userName"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="仓库" prop="level">
            <DSelect
              v-model="formData.level"
              :options="[]"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="发件人" prop="type">
            <el-input
              v-model.trim="formData.userName"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="电话" prop="companyName">
            <el-input
              v-model.trim="formData.companyName"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="邮箱" prop="companyCode">
            <el-input
              v-model.trim="formData.companyCode"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="公司名称" prop="companyAbbr">
            <el-input
              v-model.trim="formData.companyAbbr"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="国家/地区" prop="legalPersonId">
            <DSelect
              v-model="formData.level"
              :options="[]"
              placeholder="请选择"
            ></DSelect>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="省/州" prop="creditCode">
            <el-input
              v-model.trim="formData.creditCode"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="城市" prop="contactsName">
            <el-input
              v-model.trim="formData.contactsName"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="邮编" prop="contactsPhone">
            <el-input
              v-model.trim="formData.contactsPhone"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="门牌号" prop="email">
            <el-input
              v-model.trim="formData.email"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="服务商地址编码" prop="email">
            <el-input
              v-model.trim="formData.email"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="地址1" prop="detailedAddressOne">
            <el-input
              v-model.trim="formData.detailedAddressOne"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="地址2" prop="detailedAddressTwo">
            <el-input
              v-model.trim="formData.detailedAddressTwo"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirn"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
